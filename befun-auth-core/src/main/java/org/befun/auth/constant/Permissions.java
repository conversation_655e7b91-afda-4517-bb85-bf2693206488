package org.befun.auth.constant;


import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum Permissions {

    // 工作台查看编辑
    WORKBENCH_DASHBOARD_VIEW(AppVersion.FREE, "工作台/工作台/查看", PermissionPath.WORKBENCH_DASHBOARD_VIEW),
    WORKBENCH_DASHBOARD_EDIT(AppVersion.UPDATE, "工作台/工作台/编辑", PermissionPath.WORKBENCH_DASHBOARD_EDIT),

    BI_INDEX_VIEW(AppVersion.FREE, "数据洞察/数据看板/查看", PermissionPath.BI_INDEX_VIEW),
    BI_INDEX_EDIT(AppVersion.FREE, "数据洞察/数据看板/编辑", PermissionPath.BI_INDEX_EDIT),
    BI_INDEX_DOWNLOAD(AppVersion.FREE, "数据洞察/数据看板/下载图表", PermissionPath.BI_INDEX_DOWNLOAD),
    BI_DATASET_VIEW(AppVersion.FREE, "数据洞察/数据集/查看", PermissionPath.BI_DATASET_VIEW),
    BI_DATASET_EDIT(AppVersion.FREE, "数据洞察/数据集/编辑", PermissionPath.BI_DATASET_EDIT),
    BI_DATASOURCE_VIEW(AppVersion.PROFESSION, "数据洞察/数据源/查看", PermissionPath.BI_DATASOURCE_VIEW),
    BI_DATASOURCE_EDIT(AppVersion.PROFESSION, "数据洞察/数据源/编辑", PermissionPath.BI_DATASOURCE_EDIT),
    BI_DATAANALYSIS_VIEW(AppVersion.FREE, "数据洞察/文本分析/查看", PermissionPath.BI_DATAANALYSIS_VIEW),
    BI_DATAANALYSIS_EDIT(AppVersion.FREE, "数据洞察/文本分析/编辑", PermissionPath.BI_DATAANALYSIS_EDIT),

    CUSTOMER_LIFE_VIEW(AppVersion.FREE, "旅程管理/客户旅程/查看", PermissionPath.CUSTOMER_LIFE_VIEW),
    CUSTOMER_LIFE_EDIT(AppVersion.FREE, "旅程管理/客户旅程/编辑", PermissionPath.CUSTOMER_LIFE_EDIT),
    CUSTOMER_PORTRAIT_VIEW(AppVersion.FREE, "旅程管理/客户画像/查看", PermissionPath.CUSTOMER_PORTRAIT_VIEW),
    CUSTOMER_PORTRAIT_EDIT(AppVersion.FREE, "旅程管理/客户画像/编辑", PermissionPath.CUSTOMER_PORTRAIT_EDIT),
    CUSTOMER_CENTRE_VIEW(AppVersion.FREE, "客户中心/客户中心/查看", PermissionPath.CUSTOMER_CENTRE_VIEW),
    CUSTOMER_CENTRE_EDIT(AppVersion.FREE, "客户中心/客户中心/编辑", PermissionPath.CUSTOMER_CENTRE_EDIT),

    BASE_INFO_EDIT(AppVersion.FREE, "基础信息/基础信息/编辑", PermissionPath.BASE_INFO_EDIT),

    DATA_VIEW_DASHBOARD_VIEW(AppVersion.FREE, "数据洞察/数据看板/查看", PermissionPath.DATA_VIEW_DASHBOARD_VIEW),
    DATA_VIEW_DASHBOARD_EDIT(AppVersion.FREE, "数据洞察/数据看板/编辑", PermissionPath.DATA_VIEW_DASHBOARD_EDIT),
    DATA_VIEW_DATASOURCE_VIEW(AppVersion.FREE, "数据洞察/数据源/查看", PermissionPath.DATA_VIEW_DATASOURCE_VIEW),
    DATA_VIEW_DATASOURCE_EDIT(AppVersion.FREE, "数据洞察/数据源/编辑", PermissionPath.DATA_VIEW_DATASOURCE_EDIT),
    DATA_VIEW_DATA_PREPARE_VIEW(AppVersion.FREE, "数据洞察/数据准备/查看", PermissionPath.DATA_VIEW_DATA_PREPARE_VIEW),
    DATA_VIEW_DATA_PREPARE_EDIT(AppVersion.FREE, "数据洞察/数据准备/编辑", PermissionPath.DATA_VIEW_DATA_PREPARE_EDIT),

    TOUCH_MANAGE_SURVEY_VIEW(AppVersion.FREE, "数据收集/问卷管理/查看", PermissionPath.TOUCH_MANAGE_SURVEY_VIEW),
    TOUCH_MANAGE_SURVEY_EDIT(AppVersion.FREE, "数据收集/问卷管理/编辑", PermissionPath.TOUCH_MANAGE_SURVEY_EDIT),
    TOUCH_MANAGE_SURVEY_PUBLISH(AppVersion.FREE, "数据收集/问卷管理/发布", PermissionPath.TOUCH_MANAGE_SURVEY_PUBLISH),
    TOUCH_MANAGE_SURVEY_VERIFY(AppVersion.FREE, "数据收集/问卷管理/审核问卷", PermissionPath.TOUCH_MANAGE_SURVEY_VERIFY),
    TOUCH_MANAGE_SURVEY_DISTURB(AppVersion.FREE, "数据收集/问卷管理/免打扰", PermissionPath.TOUCH_MANAGE_SURVEY_DISTURB),
    TOUCH_MANAGE_SEND_MANAGE_VIEW(AppVersion.FREE, "数据收集/发送管理/查看", PermissionPath.TOUCH_MANAGE_SEND_MANAGE_VIEW),
    TOUCH_MANAGE_SEND_MANAGE_EDIT(AppVersion.PROFESSION, "数据收集/发送管理/编辑", PermissionPath.TOUCH_MANAGE_SEND_MANAGE_EDIT),

    EVENTS_EVENT_WARNING_VIEW(AppVersion.FREE, "事件中心/预警规则/查看", PermissionPath.EVENTS_EVENT_WARNING_VIEW),
    EVENTS_EVENT_WARNING_EDIT(AppVersion.UPDATE, "事件中心/预警规则/编辑", PermissionPath.EVENTS_EVENT_WARNING_EDIT),
    EVENTS_EVENT_ACTION_VIEW(AppVersion.FREE, "事件中心/事件处理/查看", PermissionPath.EVENTS_EVENT_ACTION_VIEW),
    EVENTS_EVENT_ACTION_EDIT(AppVersion.UPDATE, "事件中心/事件处理/编辑", PermissionPath.EVENTS_EVENT_ACTION_EDIT),
    EVENTS_EVENT_ACTION_GROUP(AppVersion.FREE, "事件中心/事件处理/分组", PermissionPath.EVENTS_EVENT_ACTION_GROUP),
    EVENTS_WARNING_WORD_VIEW(AppVersion.FREE, "事件中心/预警词库/查看", PermissionPath.EVENTS_WARNING_WORD_VIEW),
    EVENTS_WARNING_WORD_EDIT(AppVersion.PROFESSION, "事件中心/预警词库/编辑", PermissionPath.EVENTS_WARNING_WORD_EDIT),

    DATA_MANAGE_DATA_ANALYSIS_VIEW(AppVersion.UPDATE, "数据智能/文本分析/查看", PermissionPath.DATA_MANAGE_DATA_ANALYSIS_VIEW),
    DATA_MANAGE_DATA_ANALYSIS_EDIT(AppVersion.UPDATE, "数据智能/文本分析/编辑", PermissionPath.DATA_MANAGE_DATA_ANALYSIS_EDIT),
    DATA_MANAGE_DATA_MODULE_VIEW(AppVersion.UPDATE, "数据智能/数据模型/查看", PermissionPath.DATA_MANAGE_DATA_MODULE_VIEW),
    DATA_MANAGE_DATA_MODULE_EDIT(AppVersion.UPDATE, "数据智能/数据模型/编辑", PermissionPath.DATA_MANAGE_DATA_MODULE_EDIT),

    SYS_MANAGE_USER_MANAGE_VIEW(AppVersion.FREE, "管理后台/成员管理/查看", PermissionPath.SYS_MANAGE_USER_MANAGE_VIEW),
    SYS_MANAGE_USER_MANAGE_EDIT(AppVersion.FREE, "管理后台/成员管理/编辑", PermissionPath.SYS_MANAGE_USER_MANAGE_EDIT),
    SYS_MANAGE_USER_MANAGE_TRANSFER(AppVersion.FREE, "管理后台/成员管理/移交资源", PermissionPath.SYS_MANAGE_USER_MANAGE_TRANSFER),
    SYS_MANAGE_LEVEL_MANAGE_VIEW(AppVersion.FREE, "管理后台/组织架构/查看", PermissionPath.SYS_MANAGE_LEVEL_MANAGE_VIEW),
    SYS_MANAGE_LEVEL_MANAGE_EDIT(AppVersion.FREE, "管理后台/组织架构/编辑", PermissionPath.SYS_MANAGE_LEVEL_MANAGE_EDIT),
    SYS_MANAGE_ROLE_MANAGE_VIEW(AppVersion.FREE, "管理后台/角色管理/查看", PermissionPath.SYS_MANAGE_ROLE_MANAGE_VIEW),
    SYS_MANAGE_ROLE_MANAGE_EDIT(AppVersion.FREE, "管理后台/角色管理/编辑", PermissionPath.SYS_MANAGE_ROLE_MANAGE_EDIT),
    SYS_MANAGE_ROLE_MANAGE_ENABLE(AppVersion.FREE, "管理后台/角色管理/审核", PermissionPath.SYS_MANAGE_ROLE_MANAGE_ENABLE),

    TEMPLATE_MANAGER_SURVEY_TEMPLATE_VIEW(AppVersion.FREE, "模板管理/问卷库/查看", PermissionPath.TEMPLATE_MANAGER_SURVEY_TEMPLATE_VIEW),
    TEMPLATE_MANAGER_SURVEY_TEMPLATE_EDIT(AppVersion.FREE, "模板管理/问卷库/编辑", PermissionPath.TEMPLATE_MANAGER_SURVEY_TEMPLATE_EDIT),
    TEMPLATE_MANAGER_QUESTION_TEMPLATE_VIEW(AppVersion.FREE, "模板管理/题目库/查看", PermissionPath.TEMPLATE_MANAGER_QUESTION_TEMPLATE_VIEW),
    TEMPLATE_MANAGER_QUESTION_TEMPLATE_EDIT(AppVersion.FREE, "模板管理/题目库/编辑", PermissionPath.TEMPLATE_MANAGER_QUESTION_TEMPLATE_EDIT),
    TEMPLATE_MANAGER_MESSAGE_TEMPLATE_VIEW(AppVersion.FREE, "模板管理/消息库/查看", PermissionPath.TEMPLATE_MANAGER_MESSAGE_TEMPLATE_VIEW),
    TEMPLATE_MANAGER_MESSAGE_TEMPLATE_EDIT(AppVersion.FREE, "模板管理/消息库/编辑", PermissionPath.TEMPLATE_MANAGER_MESSAGE_TEMPLATE_EDIT),

    THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_VIEW(AppVersion.FREE, "第三方服务/第三方服务/查看", PermissionPath.THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_VIEW),
    THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_EDIT(AppVersion.FREE, "第三方服务/第三方服务/编辑", PermissionPath.THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_EDIT),

    API_MANAGER_BASIC_CONFIG_VIEW(AppVersion.FREE, "API管理/基础配置/查看", PermissionPath.API_MANAGER_BASIC_CONFIG_VIEW),
    API_MANAGER_BASIC_CONFIG_EDIT(AppVersion.FREE, "API管理/基础配置/编辑", PermissionPath.API_MANAGER_BASIC_CONFIG_EDIT),
    API_MANAGER_DATA_ACCESS_VIEW(AppVersion.FREE, "API管理/数据接入/查看", PermissionPath.API_MANAGER_DATA_ACCESS_VIEW),
    API_MANAGER_DATA_ACCESS_EDIT(AppVersion.FREE, "API管理/数据接入/编辑", PermissionPath.API_MANAGER_DATA_ACCESS_EDIT),

    WALLET_MANAGER_WALLET_RECHARGE_VIEW(AppVersion.FREE, "企业钱包/账户充值/查看", PermissionPath.WALLET_MANAGER_WALLET_RECHARGE_VIEW),
    WALLET_MANAGER_WALLET_RECHARGE_EDIT(AppVersion.FREE, "企业钱包/账户充值/编辑", PermissionPath.WALLET_MANAGER_WALLET_RECHARGE_EDIT),
    WALLET_MANAGER_CONSUMPTION_RECORDS_VIEW(AppVersion.FREE, "企业钱包/消费记录/查看", PermissionPath.WALLET_MANAGER_CONSUMPTION_RECORDS_VIEW),
    WALLET_MANAGER_CONSUMPTION_RECORDS_EDIT(AppVersion.FREE, "企业钱包/消费记录/编辑", PermissionPath.WALLET_MANAGER_CONSUMPTION_RECORDS_EDIT),

    COMMON_SETTING_EDIT(AppVersion.FREE, "通用设置/通用设置/编辑", PermissionPath.COMMON_SETTING_EDIT),
    LOG_MANAGE_VIEW(AppVersion.FREE, "日志管理/日志管理/查看", PermissionPath.LOG_MANAGE_VIEW),


    SURVEY_MANAGE_EDIT_SURVEY_EDIT(AppVersion.FREE, "", PermissionPath.SURVEY_MANAGE_EDIT_SURVEY_EDIT),
    SURVEY_MANAGE_PUBLISH_MANAGE_EDIT(AppVersion.FREE, "", PermissionPath.SURVEY_MANAGE_PUBLISH_MANAGE_EDIT),
    SURVEY_MANAGE_ANALYZE_VIEW(AppVersion.FREE, "", PermissionPath.SURVEY_MANAGE_ANALYZE_VIEW),
    SURVEY_MANAGE_ANALYZE_EDIT(AppVersion.FREE, "", PermissionPath.SURVEY_MANAGE_ANALYZE_EDIT),

    CONTACT_BIG_CONTACT_VIEW(AppVersion.FREE, "", PermissionPath.CONTACT_BIG_CONTACT_VIEW),
    CONTACT_BIG_CONTACT_EDIT(AppVersion.FREE, "", PermissionPath.CONTACT_BIG_CONTACT_EDIT),

    SYS_SURVEY_USER_MANAGE_VIEW(AppVersion.FREE, "", PermissionPath.SYS_SURVEY_USER_MANAGE_VIEW),
    SYS_SURVEY_USER_MANAGE_EDIT(AppVersion.FREE, "", PermissionPath.SYS_SURVEY_USER_MANAGE_EDIT),
    SYS_SURVEY_ROLE_MANAGE_VIEW(AppVersion.FREE, "", PermissionPath.SYS_SURVEY_ROLE_MANAGE_VIEW),
    SYS_SURVEY_ROLE_MANAGE_EDIT(AppVersion.FREE, "", PermissionPath.SYS_SURVEY_ROLE_MANAGE_EDIT),

    TOUCH_MANAGE_SURVEY_VIEW_DEPARTMENT_DATA(AppVersion.FREE, "数据收集/问卷管理/查看", PermissionPath.TOUCH_MANAGE_SURVEY_VIEW_DEPARTMENT_DATA),
    TOUCH_MANAGE_SURVEY_VIEW_ALL_DATA(AppVersion.FREE, "数据收集/问卷管理/查看", PermissionPath.TOUCH_MANAGE_SURVEY_VIEW_ALL_DATA),


    TOUCH_MANAGE_SEND_VIEW_DEPARTMENT_DATA(AppVersion.FREE, "数据收集/发送管理/查看", PermissionPath.TOUCH_MANAGE_SEND_VIEW_DEPARTMENT_DATA),
    TOUCH_MANAGE_SEND_VIEW_ALL_DATA(AppVersion.FREE, "数据收集/发送管理/查看", PermissionPath.TOUCH_MANAGE_SEND_VIEW_ALL_DATA),


    ;

    private final AppVersion minVersion;
    private final String describe;
    private final String path;

    Permissions(AppVersion minVersion, String describe, String path) {
        this.minVersion = minVersion;
        this.describe = describe;
        this.path = path;
    }

    public static List<Permissions> permissions(AppVersion version) {
        return Arrays.stream(values()).filter(i -> i.minVersion.ordinal() <= version.ordinal()).collect(Collectors.toList());
    }
}
