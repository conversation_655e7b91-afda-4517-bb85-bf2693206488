package org.befun.auth.constant;


public final class PermissionPath {

    // 工作台查看编辑
    public static final String WORKBENCH_DASHBOARD_VIEW = "/Workbench/Dashboard/view";
    public static final String WORKBENCH_DASHBOARD_EDIT = "/Workbench/Dashboard/edit";

    public static final String BI_INDEX_VIEW = "/Bi/Index/view";
    public static final String BI_INDEX_EDIT = "/Bi/Index/edit";
    public static final String BI_INDEX_DOWNLOAD = "/Bi/Index/download";
    public static final String BI_DATASOURCE_VIEW = "/Bi/Datasource/view";
    public static final String BI_DATASOURCE_EDIT = "/Bi/Datasource/edit";
    public static final String BI_DATASET_VIEW = "/Bi/Dataset/view";
    public static final String BI_DATASET_EDIT = "/Bi/Dataset/edit";
    public static final String BI_DATAANALYSIS_VIEW = "/Bi/DataAnalysis/view";
    public static final String BI_DATAANALYSIS_EDIT = "/Bi/DataAnalysis/edit";

    public static final String CUSTOMER_LIFE_VIEW = "/CustomerLife/CustomerLife/view";
    public static final String CUSTOMER_LIFE_EDIT = "/CustomerLife/CustomerLife/edit";
    public static final String CUSTOMER_PORTRAIT_VIEW = "/CustomerLife/CustomerPortrait/view";
    public static final String CUSTOMER_PORTRAIT_EDIT = "/CustomerLife/CustomerPortrait/edit";
    public static final String CUSTOMER_CENTRE_VIEW = "/CustomerCentre/CustomerCentre/view";
    public static final String CUSTOMER_CENTRE_EDIT = "/CustomerCentre/CustomerCentre/edit";

    public static final String BASE_INFO_EDIT = "/BaseInfo/BaseInfo/edit";

    public static final String DATA_VIEW_DASHBOARD_VIEW = "/DataView/Dashboard/view";
    public static final String DATA_VIEW_DASHBOARD_EDIT = "/DataView/Dashboard/edit";
    public static final String DATA_VIEW_DATASOURCE_VIEW = "/DataView/DataSource/view";
    public static final String DATA_VIEW_DATASOURCE_EDIT = "/DataView/DataSource/edit";
    public static final String DATA_VIEW_DATA_PREPARE_VIEW = "/DataView/DataPrepare/view";
    public static final String DATA_VIEW_DATA_PREPARE_EDIT = "/DataView/DataPrepare/edit";

    public static final String TOUCH_MANAGE_SURVEY_VIEW = "/TouchManager/Survey/view";
    public static final String TOUCH_MANAGE_SURVEY_EDIT = "/TouchManager/Survey/edit";
    public static final String TOUCH_MANAGE_SURVEY_PUBLISH = "/TouchManager/Survey/publish";
    public static final String TOUCH_MANAGE_SURVEY_VERIFY = "/TouchManager/Survey/verify";
    public static final String TOUCH_MANAGE_SURVEY_DISTURB = "/TouchManager/Survey/disturb";
    public static final String TOUCH_MANAGE_SEND_MANAGE_VIEW = "/TouchManager/SendManage/view";
    public static final String TOUCH_MANAGE_SEND_MANAGE_EDIT = "/TouchManager/SendManage/edit";
    public static final String TOUCH_MANAGE_SURVEY_VIEW_DEPARTMENT_DATA = "/TouchManager/Survey/viewDepartmentData";
    public static final String TOUCH_MANAGE_SURVEY_VIEW_ALL_DATA = "/TouchManager/Survey/viewAllData";

    public static final String TOUCH_MANAGE_SEND_VIEW_DEPARTMENT_DATA = "/TouchManager/Send/viewDepartmentData";
    public static final String TOUCH_MANAGE_SEND_VIEW_ALL_DATA = "/TouchManager/Send/viewAllData";

    public static final String EVENTS_EVENT_WARNING_VIEW = "/Events/EventWarning/view";
    public static final String EVENTS_EVENT_WARNING_EDIT = "/Events/EventWarning/edit";
    public static final String EVENTS_EVENT_ACTION_VIEW = "/Events/EventAction/view";
    public static final String EVENTS_EVENT_ACTION_EDIT = "/Events/EventAction/edit";
    public static final String EVENTS_EVENT_ACTION_GROUP = "/Events/EventAction/group";
    public static final String EVENTS_WARNING_WORD_VIEW = "/Events/WarningWord/view";
    public static final String EVENTS_WARNING_WORD_EDIT = "/Events/WarningWord/edit";

    public static final String DATA_MANAGE_DATA_ANALYSIS_VIEW = "/DataManage/DataAnalysis/view";
    public static final String DATA_MANAGE_DATA_ANALYSIS_EDIT = "/DataManage/DataAnalysis/edit";
    public static final String DATA_MANAGE_DATA_MODULE_VIEW = "/DataManage/DataModule/view";
    public static final String DATA_MANAGE_DATA_MODULE_EDIT = "/DataManage/DataModule/edit";

    public static final String SYS_MANAGE_USER_MANAGE_VIEW = "/SysManage/UserManage/view";
    public static final String SYS_MANAGE_USER_MANAGE_EDIT = "/SysManage/UserManage/edit";
    public static final String SYS_MANAGE_USER_MANAGE_TRANSFER = "/SysManage/UserManage/transfer";
    public static final String SYS_MANAGE_LEVEL_MANAGE_VIEW = "/SysManage/LevelManage/view";
    public static final String SYS_MANAGE_LEVEL_MANAGE_EDIT = "/SysManage/LevelManage/edit";
    public static final String SYS_MANAGE_ROLE_MANAGE_VIEW = "/SysManage/RoleManage/view";
    public static final String SYS_MANAGE_ROLE_MANAGE_EDIT = "/SysManage/RoleManage/edit";
    public static final String SYS_MANAGE_ROLE_MANAGE_ENABLE = "/SysManage/RoleManage/enable";

    public static final String TEMPLATE_MANAGER_SURVEY_TEMPLATE_VIEW = "/TemplateManager/surveyTemplate/view"; // 1.10.6
    public static final String TEMPLATE_MANAGER_SURVEY_TEMPLATE_EDIT = "/TemplateManager/surveyTemplate/edit"; // 1.10.6
    public static final String TEMPLATE_MANAGER_QUESTION_TEMPLATE_VIEW = "/TemplateManager/questionTemplate/view"; // 1.10.6
    public static final String TEMPLATE_MANAGER_QUESTION_TEMPLATE_EDIT = "/TemplateManager/questionTemplate/edit"; // 1.10.6
    public static final String TEMPLATE_MANAGER_MESSAGE_TEMPLATE_VIEW = "/TemplateManager/messageTemplate/view"; // 1.10.6
    public static final String TEMPLATE_MANAGER_MESSAGE_TEMPLATE_EDIT = "/TemplateManager/messageTemplate/edit"; // 1.10.6

    public static final String THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_VIEW = "/ThirdpartyManager/thirdpartyAccount/view"; // 1.10.6
    public static final String THIRDPARTY_MANAGER_THIRDPARTY_ACCOUNT_EDIT = "/ThirdpartyManager/thirdpartyAccount/edit"; // 1.10.6

    public static final String API_MANAGER_BASIC_CONFIG_VIEW = "/ApiManager/basicConfig/view"; // 1.10.6
    public static final String API_MANAGER_BASIC_CONFIG_EDIT = "/ApiManager/basicConfig/edit"; // 1.10.6
    public static final String API_MANAGER_DATA_ACCESS_VIEW = "/ApiManager/dataAccess/view"; // 1.10.6
    public static final String API_MANAGER_DATA_ACCESS_EDIT = "/ApiManager/dataAccess/edit"; // 1.10.6

    public static final String WALLET_MANAGER_WALLET_RECHARGE_VIEW = "/WalletManager/walletRecharge/view"; // 1.10.6
    public static final String WALLET_MANAGER_WALLET_RECHARGE_EDIT = "/WalletManager/walletRecharge/edit"; // 1.10.6
    public static final String WALLET_MANAGER_CONSUMPTION_RECORDS_VIEW = "/WalletManager/consumptionRecords/view"; // 1.10.6
    public static final String WALLET_MANAGER_CONSUMPTION_RECORDS_EDIT = "/WalletManager/consumptionRecords/edit"; // 1.10.6

    public static final String COMMON_SETTING_EDIT = "/CommonSetting/CommonSetting/edit";
    public static final String LOG_MANAGE_VIEW = "/LogManage/LogManage/view";


    public static final String SURVEY_MANAGE_EDIT_SURVEY_EDIT = "/SurveyManage/EditSurvey/edit";
    public static final String SURVEY_MANAGE_PUBLISH_MANAGE_EDIT = "/SurveyManage/PublishManage/edit";
    public static final String SURVEY_MANAGE_ANALYZE_VIEW = "/SurveyManage/Analyze/view";
    public static final String SURVEY_MANAGE_ANALYZE_EDIT = "/SurveyManage/Analyze/edit";

    public static final String CONTACT_BIG_CONTACT_VIEW = "/ContactBig/Contact/view";
    public static final String CONTACT_BIG_CONTACT_EDIT = "/ContactBig/Contact/edit";

    public static final String SYS_SURVEY_USER_MANAGE_VIEW = "/SysSurvey/UserManage/view";
    public static final String SYS_SURVEY_USER_MANAGE_EDIT = "/SysSurvey/UserManage/edit";
    public static final String SYS_SURVEY_ROLE_MANAGE_VIEW = "/SysSurvey/RoleManage/view";
    public static final String SYS_SURVEY_ROLE_MANAGE_EDIT = "/SysSurvey/RoleManage/edit";


}
