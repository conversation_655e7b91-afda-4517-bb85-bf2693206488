package org.befun.auth.service.orgconfig;

import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigBuilderDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.dto.orgconfig.OrgConfigExtendEventFieldDto;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrgConfigExtendEventFieldService implements BaseOrgConfigService {

    @Lazy
    @Autowired
    private OrganizationConfigService organizationConfigService;

    @Override
    public OrganizationConfigType type() {
        return OrganizationConfigType.extendEventField;
    }

    public Set<String> extendFieldNames() {
        return extendFields().stream().map(OrgConfigExtendEventFieldDto.ExtendField::getLabel).collect(Collectors.toSet());
    }


    public Map<String, OrgConfigExtendEventFieldDto.ExtendField> extendFieldMap() {
        Map<String, OrgConfigExtendEventFieldDto.ExtendField> map = new HashMap<>();
        extendFields().forEach(f -> {
            map.put(f.getProp(), f);
        });
        return map;
    }

    public List<OrgConfigExtendEventFieldDto.ExtendField> extendFields() {
        OrgConfigDto extendEventFieldConfig = organizationConfigService.getConfig(OrganizationConfigType.extendEventField);
        if (extendEventFieldConfig != null
                && extendEventFieldConfig.getExtendCustomerField() != null
                && extendEventFieldConfig.getExtendCustomerField().getExtendFields() != null) {
            return extendEventFieldConfig.getExtendCustomerField().getExtendFields();
        } else {
            return new ArrayList<>();
        }
    }

    public List<OrgConfigExtendEventFieldDto.ExtendField> extendFields(Long orgId) {
        OrgConfigDto extendEventFieldConfig = organizationConfigService.getConfig(orgId,OrganizationConfigType.extendEventField);
        if (extendEventFieldConfig != null
                && extendEventFieldConfig.getExtendCustomerField() != null
                && extendEventFieldConfig.getExtendCustomerField().getExtendFields() != null) {
            return extendEventFieldConfig.getExtendCustomerField().getExtendFields();
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public OrgConfigDto getDefaultConfig() {
        OrgConfigDto config = new OrgConfigDto();
        config.setExtendEventField(new OrgConfigExtendEventFieldDto());
        return config;
    }

    @Override
    public void checkConfig(OrgConfigDto data) {
        // 检验名字段是否唯一
        if (data.getExtendEventField() == null) {
            throw new BadRequestException("自定义字段列表不能为null");
        }
        List<OrgConfigExtendEventFieldDto.ExtendField> extendFields = data.getExtendEventField().getExtendFields();
        if (extendFields == null) {
            throw new BadRequestException("自定义字段列表不能为null");
        }
        List<String> duplicatedError = new ArrayList<>();
        List<String> formatError = new ArrayList<>();
        List<String> optionError = new ArrayList<>();
        List<String> decimalPlacesError = new ArrayList<>();
        extendFields.forEach(f -> {
                if (!f.getType().testFormat(f.getFormat())) {
                    formatError.add(f.getLabel());
                }
                if (f.getFormat().isHasDecimal() && f.getDecimalPlaces() == null) {
                    decimalPlacesError.add(f.getLabel());
                }
                if (f.getType().isHasOptions()) {
                    if (CollectionUtils.isEmpty(f.getOptions())) {
                        optionError.add(f.getLabel());
                    } else {
                        Set<String> optionValues = f.getOptions().stream().map(OrgConfigExtendEventFieldDto.FieldOption::getValue).collect(Collectors.toSet());
                        if (optionValues.size() != f.getOptions().size()) {
                            optionError.add(f.getLabel());
                        }
                    }
                }

        });
        List<String> s = new ArrayList<>();
        if (!formatError.isEmpty()) {
            s.add("格式错误：" + String.join(",", formatError));
        }
        if (!optionError.isEmpty()) {
            s.add("选项不能为空，并且选项值不能重复：" + String.join(",", optionError));
        }
        if (!decimalPlacesError.isEmpty()) {
            s.add("未指定保留的小数点位数：" + String.join(",", decimalPlacesError));
        }
        if (!s.isEmpty()) {
            throw new BadRequestException(String.join("；", s));
        }
    }

    @Override
    public OrgConfigBuilderDto getConfigBuilder(OrgConfigDto config) {
        return null;
    }

    @Override
    public void copyConfig(OrgConfigDto source, OrgConfigDto target) {
        target.setExtendEventField(source.getExtendEventField());
    }
}
