package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigExtendEventFieldDto  extends  OrgConfigExtendCustomerFieldDto{

    @Valid
    @NotNull
    @Schema(description = "预警规则id")
    public List<Long> eventRuleIds = new ArrayList<>();
}
