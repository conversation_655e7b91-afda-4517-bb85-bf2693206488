package org.befun.auth.dto.orgconfig;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.OrganizationConfigMfaType;

import javax.validation.Valid;

/**
 * 每次增加企业配置项时，增加一个字段，字段名称和枚举同名，字段类型和枚举定义的类型一致
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrgConfigDto {

    @Valid
    @Schema(description = "企业配置-自定义显示客户列表")
    private OrgConfigCustomerVisibleDto customerVisible;

    @Valid
    @Schema(description = "企业配置-自定义客户字段列表")
    private OrgConfigExtendCustomerFieldDto extendCustomerField;

    @Valid
    @Schema(description = "企业配置-自定义事件字段列表")
    private OrgConfigExtendEventFieldDto extendEventField;

    @Valid
    @Schema(description = "企业配置-问卷审核")
    private Boolean surveyVerify;

    @Schema(description = "企业配置-mfa安全验证")
    private OrganizationConfigMfaType mfa;

    @Schema(description = "企业配置-额外信息")
    private OrgConfigBaseInfoDto baseInfo;

    @Schema(description = "企业配置-答题端信息")
    private OrgConfigClientInfoDto clientInfo;

    @Schema(description = "企业配置-问卷内容审核")
    private Boolean surveyContentAudit;

    @Schema(description = "企业配置-事件分享")
    private Boolean eventShare;

    public OrgConfigDto(OrgConfigBaseInfoDto baseInfo) {
        this.baseInfo = baseInfo;
    }

}
